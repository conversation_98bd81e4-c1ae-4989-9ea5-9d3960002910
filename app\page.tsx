"use client"
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from "lucide-react"
import Link from "next/link"

import { Button } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"

export default function HomePage() {
  return <GlassmorphismVariation />
}

// Glassmorphism Variation with Sage Green Theme
function GlassmorphismVariation() {
  return (
    <div className="min-h-screen relative overflow-hidden">
      {/* Animated Background with Dark and Light Sage Green */}
      <div className="absolute inset-0 bg-gradient-to-br from-green-800 via-green-700 to-green-600">
        <div className="absolute inset-0 bg-gradient-to-tr from-green-700/30 via-green-500/30 to-green-400/30 animate-pulse" />

        {/* Floating Blobs */}
        {[...Array(6)].map((_, i) => (
          <div
            key={i}
            className="absolute rounded-full bg-white/10 backdrop-blur-sm animate-float"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
              width: `${Math.random() * 300 + 100}px`,
              height: `${Math.random() * 300 + 100}px`,
              animationDelay: `${Math.random() * 5}s`,
              animationDuration: `${Math.random() * 10 + 15}s`,
            }}
          />
        ))}
      </div>

      {/* Header */}
      <header className="relative z-10 container mx-auto px-4 py-6">
        <nav className="flex items-center justify-between">
          <div className="flex items-center space-x-3 group">
            <div className="w-12 h-12 bg-white/20 backdrop-blur-md rounded-2xl flex items-center justify-center shadow-lg border border-white/30 transform group-hover:scale-110 transition-all duration-300">
              <Sparkles className="w-7 h-7 text-white" />
            </div>
            <div>
              <span className="text-3xl font-bold text-white drop-shadow-lg">Vierla</span>
              <div className="text-xs text-white/80 font-medium">BEAUTY REFINED</div>
            </div>
          </div>
          <div className="hidden md:flex items-center space-x-8">
            <Link
              href="#services"
              className="text-white/90 hover:text-white transition-all duration-300 font-medium transform hover:scale-110 drop-shadow-sm"
            >
              Services
            </Link>
            <Link
              href="/about"
              className="text-white/90 hover:text-white transition-all duration-300 font-medium transform hover:scale-110 drop-shadow-sm"
            >
              About
            </Link>
            <Link
              href="/contact"
              className="text-white/90 hover:text-white transition-all duration-300 font-medium transform hover:scale-110 drop-shadow-sm"
            >
              Contact
            </Link>

          </div>
        </nav>
      </header>

      {/* Hero Section */}
      <section className="relative z-10 container mx-auto px-4 py-20">
        <div className="text-center max-w-6xl mx-auto">
          <div className="inline-flex items-center bg-white/20 backdrop-blur-md rounded-full px-6 py-3 mb-8 shadow-lg border border-white/30 animate-bounce">
            <span className="text-white font-medium drop-shadow-sm">Coming Soon</span>
          </div>

          <h1 className="text-7xl md:text-9xl font-black mb-8 leading-none text-white drop-shadow-lg">
            <span className="inline-block animate-pulse">BEAUTY</span>
            <br />
            <span className="inline-block animate-pulse" style={{ animationDelay: "0.5s" }}>
              REFINED
            </span>
          </h1>

          <p className="text-2xl md:text-3xl text-white/90 mb-12 leading-relaxed font-light max-w-4xl mx-auto drop-shadow-sm">
            Connecting service providers to customers through a comprehensive platform for beauty and self-care services.
            <br />
            <span className="text-white font-medium animate-pulse">Book. Manage. Grow.</span>
          </p>




        </div>
      </section>

      {/* Services Section - 8 Categories */}
      <section id="services" className="relative z-10 py-20">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-6xl font-black text-white mb-6 drop-shadow-lg">Our Services</h2>
            <p className="text-2xl text-white/90 max-w-3xl mx-auto drop-shadow-sm">
              Find and book beauty and self-care services from verified providers in your area
            </p>
          </div>

          <div className="grid grid-cols-2 md:grid-cols-4 gap-6 max-w-7xl mx-auto">
            {[
              {
                title: "Barbers",
                services: ["• Classic cuts", "• Beard trims", "• Hot towel shaves", "• Hair styling"],
                href: "/services/barbers",
              },
              {
                title: "Makeup",
                services: ["• Event makeup", "• Bridal looks", "• Fashion makeup", "• Everyday glam"],
                href: "/services/makeup",
              },
              {
                title: "Salons",
                services: ["• Hair cuts & color", "• Blowouts", "• Treatments", "• Full styling"],
                href: "/services/salons",
              },
              {
                title: "Locs",
                services: ["• Loc maintenance", "• Retwisting", "• Loc styling", "• Loc repair"],
                href: "/services/locs",
              },
              {
                title: "Braids",
                services: ["• Box braids", "• Cornrows", "• French braids", "• Protective styles"],
                href: "/services/braids",
              },
              {
                title: "Nails",
                services: ["• Manicures", "• Pedicures", "• Nail art", "• Gel polish"],
                href: "/services/nails",
              },
              {
                title: "Brows",
                services: ["• Eyebrow shaping", "• Threading", "• Tinting", "• Microblading"],
                href: "/services/brows",
              },
              {
                title: "Eyelashes",
                services: ["• Lash extensions", "• Lash lifts", "• Lash tinting", "• Volume lashes"],
                href: "/services/eyelashes",
              },
            ].map((service, index) => (
              <Link key={index} href={service.href}>
                <Card className="bg-white/15 backdrop-blur-md border border-white/25 shadow-xl hover:shadow-2xl transition-all duration-500 hover:-translate-y-2 rounded-2xl overflow-hidden group cursor-pointer transform hover:scale-105 h-full">
                  <CardContent className="p-6 text-center h-full flex flex-col justify-between">
                    <div>
                      <h3 className="text-xl font-bold text-white mb-4 group-hover:scale-110 transition-all duration-300 drop-shadow-sm">
                        {service.title}
                      </h3>
                      <div className="text-white/80 text-sm leading-relaxed group-hover:text-white transition-colors duration-300 drop-shadow-sm text-left">
                        {service.services.map((serviceItem, idx) => (
                          <div key={idx} className="mb-1">
                            {serviceItem}
                          </div>
                        ))}
                      </div>
                    </div>
                    <div className="mt-4 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                      <ArrowRight className="w-5 h-5 text-white mx-auto group-hover:animate-bounce" />
                    </div>
                  </CardContent>
                </Card>
              </Link>
            ))}
          </div>
        </div>
      </section>



      {/* Footer */}
      <footer className="relative z-10 bg-black/20 backdrop-blur-md text-white py-16 border-t border-white/20">
        <div className="container mx-auto px-4">
          <div className="grid md:grid-cols-4 gap-12">
            <div>
              <div className="flex items-center space-x-3 mb-6">
                <div className="w-10 h-10 bg-white/20 backdrop-blur-md rounded-2xl flex items-center justify-center border border-white/30">
                  <Sparkles className="w-6 h-6 text-white drop-shadow-sm" />
                </div>
                <span className="text-3xl font-bold drop-shadow-lg">Vierla</span>
              </div>
              <p className="text-white/80 drop-shadow-sm leading-relaxed">
                A platform connecting service providers and customers for beauty and self-care services with integrated booking, payments, and business analytics.
              </p>
            </div>

            <div>
              <h4 className="font-bold mb-6 text-xl text-white drop-shadow-sm">Services</h4>
              <ul className="space-y-3 text-white/80">
                <li>
                  <Link href="/services/barbers" className="hover:text-white transition-colors">
                    Barbers
                  </Link>
                </li>
                <li>
                  <Link href="/services/makeup" className="hover:text-white transition-colors">
                    Makeup
                  </Link>
                </li>
                <li>
                  <Link href="/services/salons" className="hover:text-white transition-colors">
                    Salons
                  </Link>
                </li>
                <li>
                  <Link href="/services/locs" className="hover:text-white transition-colors">
                    Locs
                  </Link>
                </li>
              </ul>
            </div>

            <div>
              <h4 className="font-bold mb-6 text-xl text-white drop-shadow-sm">More Services</h4>
              <ul className="space-y-3 text-white/80">
                <li>
                  <Link href="/services/braids" className="hover:text-white transition-colors">
                    Braids
                  </Link>
                </li>
                <li>
                  <Link href="/services/nails" className="hover:text-white transition-colors">
                    Nails
                  </Link>
                </li>
                <li>
                  <Link href="/services/brows" className="hover:text-white transition-colors">
                    Brows
                  </Link>
                </li>
                <li>
                  <Link href="/services/eyelashes" className="hover:text-white transition-colors">
                    Eyelashes
                  </Link>
                </li>
              </ul>
            </div>

            <div>
              <h4 className="font-bold mb-6 text-xl text-white drop-shadow-sm">Company</h4>
              <ul className="space-y-3 text-white/80">
                <li>
                  <Link href="/about" className="hover:text-white transition-colors">
                    About Us
                  </Link>
                </li>
                <li>
                  <Link href="/contact" className="hover:text-white transition-colors">
                    Contact Us
                  </Link>
                </li>
                <li>
                  <Link href="/privacy" className="hover:text-white transition-colors">
                    Privacy Policy
                  </Link>
                </li>
                <li>
                  <Link href="/terms" className="hover:text-white transition-colors">
                    Terms of Service
                  </Link>
                </li>
              </ul>
            </div>
          </div>

          <div className="border-t border-white/20 mt-12 pt-8 text-center">
            <p className="text-white/80 drop-shadow-sm">
              &copy; 2024 Vierla - Connecting Beauty Service Providers and Customers. All rights reserved.
            </p>
          </div>
        </div>
      </footer>

      <style jsx>{`
        @keyframes float {
          0%, 100% { transform: translateY(0px) rotate(0deg); }
          25% { transform: translateY(-20px) rotate(2deg); }
          50% { transform: translateY(-40px) rotate(0deg); }
          75% { transform: translateY(-20px) rotate(-2deg); }
        }
        .animate-float {
          animation: float 20s ease-in-out infinite;
        }
      `}</style>
    </div>
  )
}

