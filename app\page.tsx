"use client"
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from "lucide-react"
import Link from "next/link"

import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"

export default function HomePage() {
  return <GlassmorphismVariation />
}

// Glassmorphism Variation with Sage Green Theme
function GlassmorphismVariation() {
  return (
    <div className="min-h-screen relative overflow-hidden">
      {/* Animated Background with Dark and Light Sage Green */}
      <div className="absolute inset-0" style={{background: 'linear-gradient(to bottom right, #5A7A63, #7C9A85, #5A7A63)'}}>
        <div className="absolute inset-0 animate-pulse" style={{background: 'linear-gradient(to top right, rgba(124, 154, 133, 0.3), rgba(90, 122, 99, 0.3), rgba(124, 154, 133, 0.3))'}} />

        {/* Floating Blobs */}
        {[...Array(6)].map((_, i) => (
          <div
            key={i}
            className="absolute rounded-full bg-white/10 backdrop-blur-sm animate-float"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
              width: `${Math.random() * 300 + 100}px`,
              height: `${Math.random() * 300 + 100}px`,
              animationDelay: `${Math.random() * 5}s`,
              animationDuration: `${Math.random() * 10 + 15}s`,
            }}
          />
        ))}
      </div>

      {/* Header */}
      <header className="relative z-10 container mx-auto px-4 py-6">
        <nav className="flex items-center justify-between">
          <div className="flex items-center space-x-3 group">
            <div className="w-12 h-12 bg-white/20 backdrop-blur-md rounded-2xl flex items-center justify-center shadow-lg border border-white/30 transform group-hover:scale-110 transition-all duration-300">
              <Sparkles className="w-7 h-7 text-white" />
            </div>
            <div>
              <span className="text-3xl font-bold text-white drop-shadow-lg">Vierla</span>
              <div className="text-xs text-white/80 font-medium">YOUR SELF-CARE, SIMPLIFIED</div>
            </div>
          </div>
          <div className="hidden md:flex items-center space-x-8">
            <Link
              href="#services"
              className="text-white/90 hover:text-white transition-all duration-300 font-medium transform hover:scale-110 drop-shadow-sm"
            >
              Services
            </Link>
            <Link
              href="/about"
              className="text-white/90 hover:text-white transition-all duration-300 font-medium transform hover:scale-110 drop-shadow-sm"
            >
              About
            </Link>
            <Link
              href="/contact"
              className="text-white/90 hover:text-white transition-all duration-300 font-medium transform hover:scale-110 drop-shadow-sm"
            >
              Contact
            </Link>

          </div>
        </nav>
      </header>

      {/* Hero Section */}
      <section className="relative z-10 container mx-auto px-4 py-20">
        <div className="text-center max-w-6xl mx-auto">
          <div className="inline-flex items-center bg-white/20 backdrop-blur-md rounded-full px-6 py-3 mb-8 shadow-lg border border-white/30 animate-bounce">
            <span className="text-white font-medium drop-shadow-sm">Coming Soon</span>
          </div>

          <h1 className="text-7xl md:text-9xl font-black mb-8 leading-none text-white drop-shadow-lg">
            <span className="inline-block animate-pulse">SELF-CARE</span>
            <br />
            <span className="inline-block animate-pulse" style={{ animationDelay: "0.5s" }}>
              SIMPLIFIED
            </span>
          </h1>

          <p className="text-2xl md:text-3xl text-white/90 mb-12 leading-relaxed font-light max-w-4xl mx-auto drop-shadow-sm">
            Connecting service providers to customers through a comprehensive platform for beauty and self-care services.
            <br />
            <span className="text-white font-medium animate-pulse">Book. Manage. Grow.</span>
          </p>




        </div>
      </section>

      {/* Services Section - 8 Categories */}
      <section id="services" className="relative z-10 py-20">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-6xl font-black text-white mb-6 drop-shadow-lg">Find Your Perfect Service</h2>
            <p className="text-2xl text-white/90 max-w-4xl mx-auto drop-shadow-sm mb-4">
              Discover trusted beauty and self-care professionals in your area. Browse portfolios, read reviews, and book appointments that fit your schedule.
            </p>
            <div className="flex flex-wrap justify-center gap-4 text-lg text-white/80 max-w-3xl mx-auto">
              <span className="bg-white/20 backdrop-blur-md rounded-full px-4 py-2 border border-white/30">✓ Verified Providers</span>
              <span className="bg-white/20 backdrop-blur-md rounded-full px-4 py-2 border border-white/30">✓ Easy Booking</span>
              <span className="bg-white/20 backdrop-blur-md rounded-full px-4 py-2 border border-white/30">✓ Secure Payments</span>
              <span className="bg-white/20 backdrop-blur-md rounded-full px-4 py-2 border border-white/30">✓ Direct Messaging</span>
            </div>
          </div>

          <div className="grid grid-cols-2 md:grid-cols-4 gap-6 max-w-7xl mx-auto">
            {[
              {
                title: "Barbers",
                services: ["• Classic cuts", "• Beard trims", "• Hot towel shaves", "• Hair styling"],
                href: "/services/barbers",
              },
              {
                title: "Makeup",
                services: ["• Event makeup", "• Bridal looks", "• Fashion makeup", "• Everyday glam"],
                href: "/services/makeup",
              },
              {
                title: "Salons",
                services: ["• Hair cuts & color", "• Blowouts", "• Treatments", "• Full styling"],
                href: "/services/salons",
              },
              {
                title: "Locs",
                services: ["• Loc maintenance", "• Retwisting", "• Loc styling", "• Loc repair"],
                href: "/services/locs",
              },
              {
                title: "Braids",
                services: ["• Box braids", "• Cornrows", "• French braids", "• Protective styles"],
                href: "/services/braids",
              },
              {
                title: "Nails",
                services: ["• Manicures", "• Pedicures", "• Nail art", "• Gel polish"],
                href: "/services/nails",
              },
              {
                title: "Brows",
                services: ["• Eyebrow shaping", "• Threading", "• Tinting", "• Microblading"],
                href: "/services/brows",
              },
              {
                title: "Eyelashes",
                services: ["• Lash extensions", "• Lash lifts", "• Lash tinting", "• Volume lashes"],
                href: "/services/eyelashes",
              },
            ].map((service, index) => (
              <Link key={index} href={service.href}>
                <Card className="bg-white/15 backdrop-blur-md border border-white/25 shadow-xl hover:shadow-2xl transition-all duration-500 hover:-translate-y-2 rounded-2xl overflow-hidden group cursor-pointer transform hover:scale-105 h-full">
                  <CardContent className="p-6 text-center h-full flex flex-col justify-between">
                    <div>
                      <h3 className="text-xl font-bold text-white mb-4 group-hover:scale-110 transition-all duration-300 drop-shadow-sm">
                        {service.title}
                      </h3>
                      <div className="text-white/80 text-sm leading-relaxed group-hover:text-white transition-colors duration-300 drop-shadow-sm text-left">
                        {service.services.map((serviceItem, idx) => (
                          <div key={idx} className="mb-1">
                            {serviceItem}
                          </div>
                        ))}
                      </div>
                    </div>
                    <div className="mt-4 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                      <ArrowRight className="w-5 h-5 text-white mx-auto group-hover:animate-bounce" />
                    </div>
                  </CardContent>
                </Card>
              </Link>
            ))}
          </div>
        </div>
      </section>

      {/* Service Providers Section */}
      <section className="relative z-10 py-20">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-6xl font-black text-white mb-6 drop-shadow-lg">Grow Your Business</h2>
            <p className="text-2xl text-white/90 max-w-4xl mx-auto drop-shadow-sm mb-4">
              Join our platform and take your beauty business to the next level. Get all the tools you need to manage and grow your service business.
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8 max-w-7xl mx-auto">
            {[
              {
                title: "Digital Store",
                description: "Create your own branded online presence with portfolio showcase, service listings, and customer reviews.",
                features: ["Custom branding", "Portfolio gallery", "Service catalog", "Customer reviews"]
              },
              {
                title: "Booking Management",
                description: "Centralized appointment scheduling with calendar integration, automated reminders, and easy rescheduling.",
                features: ["Calendar sync", "Auto reminders", "Easy rescheduling", "Availability control"]
              },
              {
                title: "Business Analytics",
                description: "Track your performance with detailed insights on bookings, revenue, customer behavior, and growth trends.",
                features: ["Revenue tracking", "Customer insights", "Booking analytics", "Growth metrics"]
              },
              {
                title: "Payment Processing",
                description: "Secure payment handling with multiple payment methods and direct deposits to your preferred account.",
                features: ["Multiple payment methods", "Secure transactions", "Direct deposits", "Transaction history"]
              }
            ].map((feature, index) => (
              <div
                key={index}
                className="bg-white/15 backdrop-blur-md border border-white/25 shadow-xl hover:shadow-2xl transition-all duration-500 hover:-translate-y-2 rounded-2xl overflow-hidden group cursor-pointer transform hover:scale-105 p-8"
              >
                <div className="text-center">
                  <h3 className="text-2xl font-bold text-white mb-4 group-hover:scale-110 transition-all duration-300 drop-shadow-sm">
                    {feature.title}
                  </h3>
                  <p className="text-white/80 leading-relaxed mb-6 group-hover:text-white transition-colors duration-300 drop-shadow-sm">
                    {feature.description}
                  </p>
                  <div className="space-y-2">
                    {feature.features.map((item, idx) => (
                      <div key={idx} className="text-white/70 text-sm flex items-center justify-center">
                        <span className="w-2 h-2 bg-white/60 rounded-full mr-2"></span>
                        {item}
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            ))}
          </div>

          <div className="text-center mt-16">
            <div className="bg-white/20 backdrop-blur-md rounded-3xl p-12 shadow-2xl transform hover:scale-105 transition-all duration-500 max-w-4xl mx-auto border border-white/30">
              <h3 className="text-4xl font-black text-white mb-6 drop-shadow-lg">Ready to Start Growing?</h3>
              <p className="text-xl text-white/90 mb-8 max-w-2xl mx-auto drop-shadow-sm">
                Join thousands of beauty professionals who are already growing their businesses with Vierla.
              </p>
              <div className="flex flex-col sm:flex-row gap-6 justify-center">
                <button className="bg-white/30 backdrop-blur-md text-white hover:bg-white/40 px-12 py-4 text-lg rounded-full shadow-xl hover:shadow-2xl transition-all duration-500 transform hover:scale-110 border border-white/40 font-semibold">
                  Get Started Today
                </button>
                <button className="border-2 border-white/40 text-white hover:bg-white/20 px-12 py-4 text-lg rounded-full shadow-xl hover:shadow-2xl transition-all duration-500 transform hover:scale-110 bg-transparent backdrop-blur-md font-semibold">
                  Learn More
                </button>
              </div>
            </div>
          </div>
        </div>
      </section>



      {/* Footer */}
      <footer className="relative z-10 bg-black/20 backdrop-blur-md text-white py-16 border-t border-white/20">
        <div className="container mx-auto px-4">
          <div className="grid md:grid-cols-4 gap-12">
            <div>
              <div className="flex items-center space-x-3 mb-6">
                <div className="w-10 h-10 bg-white/20 backdrop-blur-md rounded-2xl flex items-center justify-center border border-white/30">
                  <Sparkles className="w-6 h-6 text-white drop-shadow-sm" />
                </div>
                <span className="text-3xl font-bold drop-shadow-lg">Vierla</span>
              </div>
              <p className="text-white/80 drop-shadow-sm leading-relaxed">
                A platform connecting service providers and customers for beauty and self-care services with integrated booking, payments, and business analytics.
              </p>
            </div>

            <div>
              <h4 className="font-bold mb-6 text-xl text-white drop-shadow-sm">Services</h4>
              <ul className="space-y-3 text-white/80">
                <li>
                  <Link href="/services/barbers" className="hover:text-white transition-colors">
                    Barbers
                  </Link>
                </li>
                <li>
                  <Link href="/services/makeup" className="hover:text-white transition-colors">
                    Makeup
                  </Link>
                </li>
                <li>
                  <Link href="/services/salons" className="hover:text-white transition-colors">
                    Salons
                  </Link>
                </li>
                <li>
                  <Link href="/services/locs" className="hover:text-white transition-colors">
                    Locs
                  </Link>
                </li>
              </ul>
            </div>

            <div>
              <h4 className="font-bold mb-6 text-xl text-white drop-shadow-sm">More Services</h4>
              <ul className="space-y-3 text-white/80">
                <li>
                  <Link href="/services/braids" className="hover:text-white transition-colors">
                    Braids
                  </Link>
                </li>
                <li>
                  <Link href="/services/nails" className="hover:text-white transition-colors">
                    Nails
                  </Link>
                </li>
                <li>
                  <Link href="/services/brows" className="hover:text-white transition-colors">
                    Brows
                  </Link>
                </li>
                <li>
                  <Link href="/services/eyelashes" className="hover:text-white transition-colors">
                    Eyelashes
                  </Link>
                </li>
              </ul>
            </div>

            <div>
              <h4 className="font-bold mb-6 text-xl text-white drop-shadow-sm">Company</h4>
              <ul className="space-y-3 text-white/80">
                <li>
                  <Link href="/about" className="hover:text-white transition-colors">
                    About Us
                  </Link>
                </li>
                <li>
                  <Link href="/contact" className="hover:text-white transition-colors">
                    Contact Us
                  </Link>
                </li>
                <li>
                  <Link href="/privacy" className="hover:text-white transition-colors">
                    Privacy Policy
                  </Link>
                </li>
                <li>
                  <Link href="/terms" className="hover:text-white transition-colors">
                    Terms of Service
                  </Link>
                </li>
              </ul>
            </div>
          </div>

          <div className="border-t border-white/20 mt-12 pt-8 text-center">
            <p className="text-white/80 drop-shadow-sm">
              &copy; 2024 Vierla - Your Self-Care, Simplified. All rights reserved.
            </p>
          </div>
        </div>
      </footer>

      <style jsx>{`
        @keyframes float {
          0%, 100% { transform: translateY(0px) rotate(0deg); }
          25% { transform: translateY(-20px) rotate(2deg); }
          50% { transform: translateY(-40px) rotate(0deg); }
          75% { transform: translateY(-20px) rotate(-2deg); }
        }
        .animate-float {
          animation: float 20s ease-in-out infinite;
        }
      `}</style>
    </div>
  )
}

