# Deployment Guide: Vierla on Private Debian Server

This guide provides step-by-step instructions for deploying the Vierla application on a private Debian Linux server using Docker containers with an existing NGINX reverse proxy.

## Prerequisites

- Debian Linux server with root access
- Existing NGINX reverse proxy running
- Docker and Docker Compose installed
- Domain name configured
- SSL certificates (recommended)

## Server Setup

### 1. Install Docker and Docker Compose

```bash
# Update system packages
sudo apt update && sudo apt upgrade -y

# Install Docker
sudo apt install -y apt-transport-https ca-certificates curl gnupg lsb-release
curl -fsSL https://download.docker.com/linux/debian/gpg | sudo gpg --dearmor -o /usr/share/keyrings/docker-archive-keyring.gpg
echo "deb [arch=amd64 signed-by=/usr/share/keyrings/docker-archive-keyring.gpg] https://download.docker.com/linux/debian $(lsb_release -cs) stable" | sudo tee /etc/apt/sources.list.d/docker.list > /dev/null
sudo apt update
sudo apt install -y docker-ce docker-ce-cli containerd.io

# Install Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose

# Add user to docker group
sudo usermod -aG docker $USER
newgrp docker
```

### 2. Create Application Directory

```bash
sudo mkdir -p /opt/vierla
sudo chown $USER:$USER /opt/vierla
cd /opt/vierla
```

## Application Deployment

### 1. Clone and Build Application

```bash
# Clone the repository
git clone https://gitlab.ameerosman.com/icecrown/services-application/services-app-web.git .

# Build and start the application
docker-compose up -d --build
```

### 2. Verify Container Status

```bash
# Check if containers are running
docker-compose ps

# View logs if needed
docker-compose logs -f vierla-web
```

## NGINX Reverse Proxy Configuration

### 1. Create NGINX Configuration

Create a new configuration file for Vierla:

```bash
sudo nano /etc/nginx/sites-available/vierla.conf
```

Add the following configuration (replace `your-domain.com` with your actual domain):

```nginx
server {
    listen 80;
    server_name your-domain.com www.your-domain.com;
    
    # Redirect HTTP to HTTPS
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name your-domain.com www.your-domain.com;

    # SSL Configuration (adjust paths to your certificates)
    ssl_certificate /path/to/your/certificate.crt;
    ssl_certificate_key /path/to/your/private.key;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;

    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;

    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied expired no-cache no-store private must-revalidate auth;
    gzip_types text/plain text/css text/xml text/javascript application/x-javascript application/xml+rss application/javascript;

    # Proxy to Docker container
    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        proxy_read_timeout 86400;
    }

    # Static assets caching
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
        proxy_pass http://localhost:3000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}
```

### 2. Enable the Configuration

```bash
# Enable the site
sudo ln -s /etc/nginx/sites-available/vierla.conf /etc/nginx/sites-enabled/

# Test NGINX configuration
sudo nginx -t

# Reload NGINX
sudo systemctl reload nginx
```

## DNS Configuration

### 1. DNS Provider Settings

Configure your DNS provider to point your domain to your server:

- **A Record**: `your-domain.com` → `YOUR_SERVER_IP`
- **CNAME Record**: `www.your-domain.com` → `your-domain.com`

### 2. Router Configuration

If your server is behind a router/firewall:

1. **Port Forwarding**: Forward ports 80 and 443 to your server's internal IP
2. **Firewall Rules**: Ensure ports 80 and 443 are open

## SSL Certificate Setup

### Option 1: Let's Encrypt (Recommended)

```bash
# Install Certbot
sudo apt install -y certbot python3-certbot-nginx

# Obtain SSL certificate
sudo certbot --nginx -d your-domain.com -d www.your-domain.com

# Auto-renewal setup (usually automatic)
sudo crontab -e
# Add: 0 12 * * * /usr/bin/certbot renew --quiet
```

### Option 2: Custom SSL Certificate

If using custom certificates, update the SSL paths in the NGINX configuration:

```nginx
ssl_certificate /path/to/your/certificate.crt;
ssl_certificate_key /path/to/your/private.key;
```

## Monitoring and Maintenance

### 1. Application Logs

```bash
# View application logs
docker-compose logs -f vierla-web

# View NGINX logs
sudo tail -f /var/log/nginx/access.log
sudo tail -f /var/log/nginx/error.log
```

### 2. Updates and Maintenance

```bash
# Update application
cd /opt/vierla
git pull origin main
docker-compose down
docker-compose up -d --build

# Backup before updates (recommended)
docker-compose exec vierla-web tar -czf /tmp/backup-$(date +%Y%m%d).tar.gz /app
```

### 3. Health Checks

```bash
# Check container health
docker-compose ps
docker stats

# Check NGINX status
sudo systemctl status nginx

# Test application accessibility
curl -I https://your-domain.com
```

## Troubleshooting

### Common Issues

1. **Container won't start**: Check logs with `docker-compose logs vierla-web`
2. **502 Bad Gateway**: Verify container is running and port 3000 is accessible
3. **SSL issues**: Check certificate paths and permissions
4. **DNS not resolving**: Verify DNS propagation with `nslookup your-domain.com`

### Useful Commands

```bash
# Restart services
docker-compose restart
sudo systemctl restart nginx

# Check port usage
sudo netstat -tlnp | grep :3000
sudo netstat -tlnp | grep :80

# Check firewall status
sudo ufw status
```

## Security Considerations

1. **Firewall**: Configure UFW to only allow necessary ports
2. **Updates**: Keep system and Docker images updated
3. **Backups**: Regular backups of application data
4. **Monitoring**: Set up monitoring for uptime and performance
5. **Access Control**: Limit SSH access and use key-based authentication

## Performance Optimization

1. **Resource Limits**: Set appropriate Docker resource limits
2. **Caching**: Implement Redis for session/data caching if needed
3. **CDN**: Consider using a CDN for static assets
4. **Database**: Optimize database queries and indexing
5. **Monitoring**: Use tools like Prometheus/Grafana for monitoring
